# Generated by Django 4.2 on 2024-12-04 12:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0046_discount_inbound_market_share'),
    ]

    operations = [
        migrations.AlterField(
            model_name='discountparameter',
            name='bound_type',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (1, 'VOLUME'),
                    (2, 'FINANCIAL_COMMITMENT'),
                    (3, 'MARKET_SHARE'),
                    (5, 'UNIQUE_IMSI_COUNT_PER_MONTH'),
                    (6, 'VOLUME_INCLUDED_IN_ACCESS_FEE'),
                    (7, 'FINANCIAL_THRESHOLD'),
                ],
                null=True,
                verbose_name='Bound Type'
            ),
        ),
    ]
