from typing import Optional

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.iotron.commands import ProcessExternalAgreementCommand
from nga.apps.iotron.domain.repositories.external_agreements import AbstractExternalAgreementRepository
from nga.apps.iotron.enums import ExternalAgreementProcessingStatusEnum
from nga.infra.celery import celery_app
import logging

logger = logging.getLogger(__name__)




@celery_app.task
@inject
def process_external_agreement(
    processing_status: Optional[
        ExternalAgreementProcessingStatusEnum
    ] = ExternalAgreementProcessingStatusEnum.NOT_PROCESSED,
    external_agreement_ids: Optional[list[int]] = None,
    mediator: Mediator = Provide["mediator"],
    external_agreement_repository: AbstractExternalAgreementRepository = Closing[
        Provide["external_agreement_repository"]
    ],
    budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
) -> None:
    
    
    
    master_budget = budget_repository.get_master()

    logger.debug("Some debug info: %s", master_budget)

    non_processed_agreements = external_agreement_repository.get_many(
        ids=external_agreement_ids, processing_status=processing_status
    )

    for external_agreement in non_processed_agreements:

        logger.debug("Processing agreement: %s", external_agreement.id)
        
        agreement_processing_cmd = ProcessExternalAgreementCommand(
            external_agreement.id,
            budget_id=master_budget.id,
        )

        processed_agreement = mediator.send(agreement_processing_cmd)

        external_agreement_repository.save(processed_agreement)
